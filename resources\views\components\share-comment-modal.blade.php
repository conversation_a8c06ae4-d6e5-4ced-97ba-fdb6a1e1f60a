@props(['share'])

<!-- Share Comment Modal -->
<div id="shareCommentModal-{{ $share->id }}" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 hidden z-50" onclick="closeShareCommentModal({{ $share->id }}, event)">
    <div class="relative w-full max-w-4xl bg-white rounded-lg shadow-2xl flex flex-col overflow-hidden" style="height: 90vh; max-height: 90vh;" onclick="event.stopPropagation()">
        <!-- Modal Header -->
        <div class="flex items-center justify-between p-4 border-b border-gray-200 flex-shrink-0">
            <h3 class="text-lg font-medium text-gray-900">
                {{ $share->user->name }}'s shared post
            </h3>
            <button onclick="closeShareCommentModal({{ $share->id }})" class="text-gray-400 hover:text-gray-600 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <!-- Modal Content - Scrollable -->
        <div class="flex-1 overflow-y-auto">
            <!-- Share Header in Modal -->
            <div class="p-4 border-b border-gray-200 bg-gray-50">
                <div class="flex items-center space-x-3">
                    <a href="{{ route('profile.user', $share->user) }}">
                        <img class="h-12 w-12 rounded-full hover:opacity-80 transition-opacity" 
                             src="{{ $share->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->user->name) . '&color=7BC74D&background=EEEEEE' }}" 
                             alt="{{ $share->user->name }}">
                    </a>
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <a href="{{ route('profile.user', $share->user) }}" class="font-semibold text-gray-900 hover:text-custom-green">
                                {{ $share->user->name }}
                            </a>
                            <span class="text-gray-600">shared a post</span>
                        </div>
                        <div class="text-sm text-gray-500">
                            {{ $share->created_at->diffForHumans() }}
                        </div>
                    </div>
                </div>
                
                <!-- Share Message (if any) -->
                @if($share->message)
                    <div class="mt-3 text-gray-700">
                        <p>{!! nl2br(e($share->message)) !!}</p>
                    </div>
                @endif
            </div>

            <!-- Original Post Preview in Modal -->
            <div class="p-4 border-b border-gray-200">
                <div class="border border-gray-200 rounded-lg overflow-hidden">
                    <!-- Original Post Header -->
                    <div class="p-4 bg-gray-50 border-b border-gray-200">
                        <div class="flex items-center space-x-3">
                            @if($share->post->organization)
                                <a href="{{ route('organizations.show', $share->post->organization) }}">
                                    <img class="h-10 w-10 rounded-full hover:opacity-80 transition-opacity" 
                                         src="{{ $share->post->organization->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->organization->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->organization->name) . '&color=3B82F6&background=DBEAFE' }}" 
                                         alt="{{ $share->post->organization->name }}">
                                </a>
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2">
                                        <a href="{{ route('organizations.show', $share->post->organization) }}" class="font-semibold text-gray-900 hover:text-custom-green">
                                            {{ $share->post->organization->name }}
                                        </a>
                                        <span class="text-gray-500 text-sm">•</span>
                                        <a href="{{ route('profile.user', $share->post->user) }}" class="text-sm text-gray-600 hover:text-custom-green">
                                            by {{ $share->post->user->name }}
                                        </a>
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        {{ $share->post->published_at->diffForHumans() }}
                                    </div>
                                </div>
                            @else
                                <a href="{{ route('profile.user', $share->post->user) }}">
                                    <img class="h-10 w-10 rounded-full hover:opacity-80 transition-opacity" 
                                         src="{{ $share->post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->user->name) . '&color=7BC74D&background=EEEEEE' }}" 
                                         alt="{{ $share->post->user->name }}">
                                </a>
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2">
                                        <a href="{{ route('profile.user', $share->post->user) }}" class="font-semibold text-gray-900 hover:text-custom-green">
                                            {{ $share->post->user->name }}
                                        </a>
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        {{ $share->post->published_at->diffForHumans() }}
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Original Post Content -->
                    <div class="p-4 bg-white">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $share->post->title }}</h3>
                        
                        @if($share->post->content)
                            <div class="text-gray-700 mb-3">
                                <p>{{ $share->post->content }}</p>
                            </div>
                        @endif

                        <!-- Post Images -->
                        @if($share->post->images && count($share->post->images) > 0)
                            <div class="mb-3">
                                @if(count($share->post->images) == 1)
                                    <div class="rounded-lg overflow-hidden">
                                        <img src="{{ \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->images[0]) }}" 
                                             alt="Post image" 
                                             class="w-full h-64 object-cover">
                                    </div>
                                @else
                                    <div class="grid grid-cols-2 gap-2 rounded-lg overflow-hidden">
                                        @foreach(array_slice($share->post->images, 0, 4) as $index => $image)
                                            <div class="relative {{ $index >= 2 ? 'hidden sm:block' : '' }}">
                                                <img src="{{ \Illuminate\Support\Facades\Storage::disk('public')->url($image) }}" 
                                                     alt="Post image {{ $index + 1 }}" 
                                                     class="w-full h-32 object-cover">
                                                @if($index == 3 && count($share->post->images) > 4)
                                                    <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                                                        <span class="text-white font-semibold">+{{ count($share->post->images) - 4 }}</span>
                                                    </div>
                                                @endif
                                            </div>
                                        @endforeach
                                    </div>
                                @endif
                            </div>
                        @endif

                        <!-- Post Type Badge -->
                        @if($share->post->type !== 'general')
                            <div class="mb-3">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    @if($share->post->type === 'announcement') bg-blue-100 text-blue-800
                                    @elseif($share->post->type === 'event') bg-green-100 text-green-800
                                    @elseif($share->post->type === 'job') bg-purple-100 text-purple-800
                                    @elseif($share->post->type === 'scholarship') bg-yellow-100 text-yellow-800
                                    @else bg-gray-100 text-gray-800
                                    @endif">
                                    {{ ucfirst($share->post->type) }}
                                </span>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="px-4 py-3 border-b border-gray-200">
                <div class="flex items-center justify-around">
                    <!-- Facebook-style Reactions for SHARE -->
                    <x-facebook-reactions :target="$share" target-type="share" />

                    <button class="flex items-center space-x-2 text-gray-600 hover:text-blue-500 transition-colors py-2 px-4 rounded-lg hover:bg-gray-100">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                        <span class="font-medium">Comment</span>
                    </button>

                    <button onclick="openShareModal({{ $share->post->id }})" class="flex items-center space-x-2 text-gray-600 hover:text-blue-500 transition-colors py-2 px-4 rounded-lg hover:bg-gray-100">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                        </svg>
                        <span class="font-medium">Share</span>
                    </button>
                </div>
            </div>

            <!-- Comments Section -->
            <div class="flex-1 overflow-y-auto">
                <x-share-comment-section :share="$share" :showInline="false" />
            </div>
        </div>

        <!-- Add Comment Form - Fixed at bottom -->
        @auth
        <div class="flex-shrink-0 p-4 border-t border-gray-200 bg-white">
            <form class="share-comment-form" data-share-id="{{ $share->id }}">
                @csrf
                <div class="flex space-x-3">
                    <div class="flex-shrink-0">
                        <img class="h-8 w-8 rounded-full"
                             src="{{ auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}"
                             alt="{{ auth()->user()->name }}">
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="relative">
                            <textarea name="content" rows="1"
                                      placeholder="Write a comment..."
                                      class="w-full bg-gray-50 text-gray-900 border-2 border-gray-300 rounded-full px-4 py-2 pr-12 focus:outline-none focus:ring-2 focus:ring-custom-green focus:border-transparent resize-none"
                                      style="min-height: 40px;" required></textarea>
                            <div class="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 transition-opacity duration-200" id="modal-share-comment-submit-btn-{{ $share->id }}">
                                <button type="submit"
                                        class="p-2 bg-custom-green text-white rounded-full hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green shadow-sm transition-all duration-200 hover:scale-105">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        @else
        <div class="flex-shrink-0 p-4 border-t border-gray-200 bg-white text-center">
            <p class="text-gray-700 mb-2">Please log in to comment</p>
            <a href="{{ route('login') }}" class="text-custom-green hover:text-green-400 font-semibold">Log in</a>
        </div>
        @endauth
    </div>
</div>
