<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['share']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['share']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<!-- Share Comment Modal -->
<div id="shareCommentModal-<?php echo e($share->id); ?>" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 hidden z-50" onclick="closeShareCommentModal(<?php echo e($share->id); ?>, event)">
    <div class="relative w-full max-w-4xl bg-white rounded-lg shadow-2xl flex flex-col overflow-hidden" style="height: 90vh; max-height: 90vh;" onclick="event.stopPropagation()">
        <!-- Modal Header -->
        <div class="flex items-center justify-between p-4 border-b border-gray-200 flex-shrink-0">
            <h3 class="text-lg font-medium text-gray-900">
                <?php echo e($share->user->name); ?>'s shared post
            </h3>
            <button onclick="closeShareCommentModal(<?php echo e($share->id); ?>)" class="text-gray-400 hover:text-gray-600 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <!-- Modal Content - Scrollable -->
        <div class="flex-1 overflow-y-auto">
            <!-- Share Header in Modal -->
            <div class="p-4 border-b border-gray-200 bg-gray-50">
                <div class="flex items-center space-x-3">
                    <a href="<?php echo e(route('profile.user', $share->user)); ?>">
                        <img class="h-12 w-12 rounded-full hover:opacity-80 transition-opacity" 
                             src="<?php echo e($share->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->user->name) . '&color=7BC74D&background=EEEEEE'); ?>" 
                             alt="<?php echo e($share->user->name); ?>">
                    </a>
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <a href="<?php echo e(route('profile.user', $share->user)); ?>" class="font-semibold text-gray-900 hover:text-custom-green">
                                <?php echo e($share->user->name); ?>

                            </a>
                            <span class="text-gray-600">shared a post</span>
                        </div>
                        <div class="text-sm text-gray-500">
                            <?php echo e($share->created_at->diffForHumans()); ?>

                        </div>
                    </div>
                </div>
                
                <!-- Share Message (if any) -->
                <?php if($share->message): ?>
                    <div class="mt-3 text-gray-700">
                        <p><?php echo nl2br(e($share->message)); ?></p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Original Post Preview in Modal -->
            <div class="p-4 border-b border-gray-200">
                <div class="border border-gray-200 rounded-lg overflow-hidden">
                    <!-- Original Post Header -->
                    <div class="p-4 bg-gray-50 border-b border-gray-200">
                        <div class="flex items-center space-x-3">
                            <?php if($share->post->organization): ?>
                                <a href="<?php echo e(route('organizations.show', $share->post->organization)); ?>">
                                    <img class="h-10 w-10 rounded-full hover:opacity-80 transition-opacity" 
                                         src="<?php echo e($share->post->organization->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->organization->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->organization->name) . '&color=3B82F6&background=DBEAFE'); ?>" 
                                         alt="<?php echo e($share->post->organization->name); ?>">
                                </a>
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2">
                                        <a href="<?php echo e(route('organizations.show', $share->post->organization)); ?>" class="font-semibold text-gray-900 hover:text-custom-green">
                                            <?php echo e($share->post->organization->name); ?>

                                        </a>
                                        <span class="text-gray-500 text-sm">•</span>
                                        <a href="<?php echo e(route('profile.user', $share->post->user)); ?>" class="text-sm text-gray-600 hover:text-custom-green">
                                            by <?php echo e($share->post->user->name); ?>

                                        </a>
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        <?php echo e($share->post->published_at->diffForHumans()); ?>

                                    </div>
                                </div>
                            <?php else: ?>
                                <a href="<?php echo e(route('profile.user', $share->post->user)); ?>">
                                    <img class="h-10 w-10 rounded-full hover:opacity-80 transition-opacity" 
                                         src="<?php echo e($share->post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->user->name) . '&color=7BC74D&background=EEEEEE'); ?>" 
                                         alt="<?php echo e($share->post->user->name); ?>">
                                </a>
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2">
                                        <a href="<?php echo e(route('profile.user', $share->post->user)); ?>" class="font-semibold text-gray-900 hover:text-custom-green">
                                            <?php echo e($share->post->user->name); ?>

                                        </a>
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        <?php echo e($share->post->published_at->diffForHumans()); ?>

                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Original Post Content -->
                    <div class="p-4 bg-white">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2"><?php echo e($share->post->title); ?></h3>
                        
                        <?php if($share->post->content): ?>
                            <div class="text-gray-700 mb-3">
                                <p><?php echo e($share->post->content); ?></p>
                            </div>
                        <?php endif; ?>

                        <!-- Post Images -->
                        <?php if($share->post->images && count($share->post->images) > 0): ?>
                            <div class="mb-3">
                                <?php if(count($share->post->images) == 1): ?>
                                    <div class="rounded-lg overflow-hidden">
                                        <img src="<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($share->post->images[0])); ?>" 
                                             alt="Post image" 
                                             class="w-full h-64 object-cover">
                                    </div>
                                <?php else: ?>
                                    <div class="grid grid-cols-2 gap-2 rounded-lg overflow-hidden">
                                        <?php $__currentLoopData = array_slice($share->post->images, 0, 4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="relative <?php echo e($index >= 2 ? 'hidden sm:block' : ''); ?>">
                                                <img src="<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($image)); ?>" 
                                                     alt="Post image <?php echo e($index + 1); ?>" 
                                                     class="w-full h-32 object-cover">
                                                <?php if($index == 3 && count($share->post->images) > 4): ?>
                                                    <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                                                        <span class="text-white font-semibold">+<?php echo e(count($share->post->images) - 4); ?></span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                        <!-- Post Type Badge -->
                        <?php if($share->post->type !== 'general'): ?>
                            <div class="mb-3">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    <?php if($share->post->type === 'announcement'): ?> bg-blue-100 text-blue-800
                                    <?php elseif($share->post->type === 'event'): ?> bg-green-100 text-green-800
                                    <?php elseif($share->post->type === 'job'): ?> bg-purple-100 text-purple-800
                                    <?php elseif($share->post->type === 'scholarship'): ?> bg-yellow-100 text-yellow-800
                                    <?php else: ?> bg-gray-100 text-gray-800
                                    <?php endif; ?>">
                                    <?php echo e(ucfirst($share->post->type)); ?>

                                </span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="px-4 py-3 border-b border-gray-200">
                <div class="flex items-center justify-around">
                    <!-- Facebook-style Reactions for SHARE -->
                    <?php if (isset($component)) { $__componentOriginala21421101ecd1a67ffa8905ddbe200b3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala21421101ecd1a67ffa8905ddbe200b3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.facebook-reactions','data' => ['target' => $share,'targetType' => 'share']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('facebook-reactions'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['target' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($share),'target-type' => 'share']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala21421101ecd1a67ffa8905ddbe200b3)): ?>
<?php $attributes = $__attributesOriginala21421101ecd1a67ffa8905ddbe200b3; ?>
<?php unset($__attributesOriginala21421101ecd1a67ffa8905ddbe200b3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala21421101ecd1a67ffa8905ddbe200b3)): ?>
<?php $component = $__componentOriginala21421101ecd1a67ffa8905ddbe200b3; ?>
<?php unset($__componentOriginala21421101ecd1a67ffa8905ddbe200b3); ?>
<?php endif; ?>

                    <button class="flex items-center space-x-2 text-gray-600 hover:text-blue-500 transition-colors py-2 px-4 rounded-lg hover:bg-gray-100">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                        <span class="font-medium">Comment</span>
                    </button>

                    <button onclick="openShareModal(<?php echo e($share->post->id); ?>)" class="flex items-center space-x-2 text-gray-600 hover:text-blue-500 transition-colors py-2 px-4 rounded-lg hover:bg-gray-100">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                        </svg>
                        <span class="font-medium">Share</span>
                    </button>
                </div>
            </div>

            <!-- Comments Section -->
            <div class="flex-1 overflow-y-auto">
                <?php if (isset($component)) { $__componentOriginale2adaab6a901bf856821fcceeedc4714 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale2adaab6a901bf856821fcceeedc4714 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.share-comment-section','data' => ['share' => $share,'showInline' => false]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('share-comment-section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['share' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($share),'showInline' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale2adaab6a901bf856821fcceeedc4714)): ?>
<?php $attributes = $__attributesOriginale2adaab6a901bf856821fcceeedc4714; ?>
<?php unset($__attributesOriginale2adaab6a901bf856821fcceeedc4714); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale2adaab6a901bf856821fcceeedc4714)): ?>
<?php $component = $__componentOriginale2adaab6a901bf856821fcceeedc4714; ?>
<?php unset($__componentOriginale2adaab6a901bf856821fcceeedc4714); ?>
<?php endif; ?>
            </div>
        </div>


    </div>
</div>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/components/share-comment-modal.blade.php ENDPATH**/ ?>