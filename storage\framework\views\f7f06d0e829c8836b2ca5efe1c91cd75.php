<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['share']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['share']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="post-card bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
     data-type="share"
     data-share-id="<?php echo e($share->id); ?>"
     data-post-id="<?php echo e($share->post->id); ?>">
    <!-- Share Header -->
    <div class="p-4 border-b border-gray-200">
        <div class="flex items-center space-x-3">
            <a href="<?php echo e(route('profile.user', $share->user)); ?>">
                <img class="h-12 w-12 rounded-full hover:opacity-80 transition-opacity"
                     src="<?php echo e($share->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->user->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                     alt="<?php echo e($share->user->name); ?>">
            </a>
            <div class="flex-1">
                <div class="flex items-center space-x-2">
                    <a href="<?php echo e(route('profile.user', $share->user)); ?>" class="font-semibold text-gray-900 hover:text-custom-green">
                        <?php echo e($share->user->name); ?>

                    </a>
                    <span class="text-gray-600">shared a post</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <time datetime="<?php echo e($share->created_at->toISOString()); ?>">
                            <?php echo e($share->created_at->diffForHumans()); ?>

                        </time>
                        <span>•</span>

                        <!-- Privacy Scope Indicator -->
                        <?php
                            $scopeDetails = $share->getPrivacyScopeDetails();
                        ?>
                        <div class="flex items-center space-x-1" title="<?php echo e($scopeDetails['description']); ?>">
                            <?php if($share->privacy_scope === 'public'): ?>
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            <?php elseif($share->privacy_scope === 'friends'): ?>
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                </svg>
                            <?php elseif($share->privacy_scope === 'only_me'): ?>
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                </svg>
                            <?php else: ?>
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            <?php endif; ?>
                            <span class="text-xs"><?php echo e($scopeDetails['label']); ?></span>
                        </div>
                    </div>

                    <!-- Share Actions Dropdown -->
                    <?php if(auth()->check() && (auth()->id() === $share->user_id || auth()->user()->isAdmin())): ?>
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="text-gray-400 hover:text-gray-600">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                </svg>
                            </button>

                            <div x-show="open" @click.away="open = false" x-transition
                                 class="absolute right-0 top-8 w-48 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-10">
                                <button onclick="editShareMessage(<?php echo e($share->id); ?>)"
                                        class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                    <span>Edit message</span>
                                </button>
                                <button onclick="deleteShare(<?php echo e($share->id); ?>)"
                                        class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                    <span>Delete share</span>
                                </button>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Share Message (if any) -->
        <?php if($share->message): ?>
            <div class="mt-3 text-gray-700" id="share-message-<?php echo e($share->id); ?>">
                <p><?php echo nl2br(e($share->message)); ?></p>
            </div>
        <?php endif; ?>

        <!-- Edit Share Message Form (hidden by default) -->
        <div class="mt-3 hidden" id="edit-share-form-<?php echo e($share->id); ?>">
            <form class="edit-share-form" data-share-id="<?php echo e($share->id); ?>">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>

                <!-- Privacy Scope Selector for Edit -->
                <div class="mb-3">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Privacy</label>
                    <div class="relative" x-data="{ open: false, selected: '<?php echo e($share->privacy_scope); ?>' }">
                        <button type="button" @click="open = !open"
                                class="w-full flex items-center justify-between px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            <div class="flex items-center space-x-2">
                                <svg x-show="selected === 'public'" class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <svg x-show="selected === 'friends'" class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                </svg>
                                <svg x-show="selected === 'only_me'" class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                </svg>
                                <svg x-show="selected === 'custom'" class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                <span x-text="selected === 'public' ? 'Public' : selected === 'friends' ? 'Friends' : selected === 'only_me' ? 'Only me' : 'Custom'"></span>
                            </div>
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>

                        <div x-show="open" @click.away="open = false" x-transition
                             class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md py-1 text-base ring-1 ring-black ring-opacity-5">
                            <div @click="selected = 'public'; open = false" class="cursor-pointer py-2 pl-3 pr-9 hover:bg-gray-50">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span class="text-sm">Public</span>
                                </div>
                            </div>
                            <div @click="selected = 'friends'; open = false" class="cursor-pointer py-2 pl-3 pr-9 hover:bg-gray-50">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                    </svg>
                                    <span class="text-sm">Friends</span>
                                </div>
                            </div>
                            <div @click="selected = 'only_me'; open = false" class="cursor-pointer py-2 pl-3 pr-9 hover:bg-gray-50">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                    </svg>
                                    <span class="text-sm">Only me</span>
                                </div>
                            </div>
                            <div @click="selected = 'custom'; open = false" class="cursor-pointer py-2 pl-3 pr-9 hover:bg-gray-50">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    <span class="text-sm">Custom</span>
                                </div>
                            </div>
                        </div>

                        <input type="hidden" name="privacy_scope" :value="selected">
                    </div>
                </div>

                <textarea name="message" rows="3"
                          placeholder="Edit your message..."
                          class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none"><?php echo e($share->message); ?></textarea>
                <div class="mt-2 flex justify-end space-x-2">
                    <button type="button" onclick="cancelEditShare(<?php echo e($share->id); ?>)"
                            class="px-3 py-1 text-sm text-gray-600 hover:text-gray-800">Cancel</button>
                    <button type="submit"
                            class="px-3 py-1 bg-custom-green text-white text-sm font-medium rounded hover:bg-custom-second-darkest">
                        Save
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Original Post Content (Embedded) -->
    <div class="mx-4 mb-4 border border-gray-200 rounded-lg overflow-hidden">
        <!-- Original Post Header -->
        <div class="p-4 bg-gray-50 border-b border-gray-200">
            <div class="flex items-center space-x-3">
                <?php if($share->post->organization): ?>
                    <a href="<?php echo e(route('organizations.show', $share->post->organization)); ?>">
                        <img class="h-10 w-10 rounded-full hover:opacity-80 transition-opacity"
                             src="<?php echo e($share->post->organization->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->organization->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->organization->name) . '&color=3B82F6&background=DBEAFE'); ?>"
                             alt="<?php echo e($share->post->organization->name); ?>">
                    </a>
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <a href="<?php echo e(route('organizations.show', $share->post->organization)); ?>" class="font-semibold text-gray-900 hover:text-custom-green">
                                <?php echo e($share->post->organization->name); ?>

                            </a>
                            <span class="text-gray-500 text-sm">•</span>
                            <a href="<?php echo e(route('profile.user', $share->post->user)); ?>" class="text-sm text-gray-600 hover:text-custom-green">
                                by <?php echo e($share->post->user->name); ?>

                            </a>
                        </div>
                        <div class="text-sm text-gray-500">
                            <?php echo e($share->post->published_at->diffForHumans()); ?>

                        </div>
                    </div>
                <?php else: ?>
                    <a href="<?php echo e(route('profile.user', $share->post->user)); ?>">
                        <img class="h-10 w-10 rounded-full hover:opacity-80 transition-opacity"
                             src="<?php echo e($share->post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->user->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                             alt="<?php echo e($share->post->user->name); ?>">
                    </a>
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <a href="<?php echo e(route('profile.user', $share->post->user)); ?>" class="font-semibold text-gray-900 hover:text-custom-green">
                                <?php echo e($share->post->user->name); ?>

                            </a>
                        </div>
                        <div class="text-sm text-gray-500">
                            <?php echo e($share->post->published_at->diffForHumans()); ?>

                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Original Post Content -->
        <div class="p-4 bg-white">
            <a href="<?php echo e(route('posts.show', $share->post)); ?>" class="block hover:bg-gray-50 transition-colors rounded p-2 -m-2">
                <div class="flex items-start justify-between">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2 flex-1"><?php echo e($share->post->title); ?></h3>
                    <svg class="w-5 h-5 text-gray-400 ml-2 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                </div>
                
                <?php if($share->post->content): ?>
                    <div class="text-gray-700 mb-3">
                        <p><?php echo e(Str::limit($share->post->content, 200)); ?></p>
                    </div>
                <?php endif; ?>

                <!-- Post Images -->
                <?php if($share->post->images && count($share->post->images) > 0): ?>
                    <div class="mb-3">
                        <?php if(count($share->post->images) == 1): ?>
                            <div class="rounded-lg overflow-hidden">
                                <img src="<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($share->post->images[0])); ?>" 
                                     alt="Post image" 
                                     class="w-full h-64 object-cover">
                            </div>
                        <?php else: ?>
                            <div class="grid grid-cols-2 gap-2 rounded-lg overflow-hidden">
                                <?php $__currentLoopData = array_slice($share->post->images, 0, 4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="relative <?php echo e($index >= 2 ? 'hidden sm:block' : ''); ?>">
                                        <img src="<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($image)); ?>" 
                                             alt="Post image <?php echo e($index + 1); ?>" 
                                             class="w-full h-32 object-cover">
                                        <?php if($index == 3 && count($share->post->images) > 4): ?>
                                            <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                                                <span class="text-white font-semibold">+<?php echo e(count($share->post->images) - 4); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <!-- Post Type Badge -->
                <?php if($share->post->type !== 'general'): ?>
                    <div class="mb-3">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            <?php if($share->post->type === 'announcement'): ?> bg-blue-100 text-blue-800
                            <?php elseif($share->post->type === 'event'): ?> bg-green-100 text-green-800
                            <?php elseif($share->post->type === 'job'): ?> bg-purple-100 text-purple-800
                            <?php elseif($share->post->type === 'scholarship'): ?> bg-yellow-100 text-yellow-800
                            <?php else: ?> bg-gray-100 text-gray-800
                            <?php endif; ?>">
                            <?php echo e(ucfirst($share->post->type)); ?>

                        </span>
                    </div>
                <?php endif; ?>
            </a>
        </div>
    </div>

    <!-- Reaction Summary Bar for SHARE -->
    <?php
        $shareReactions = $share->reactions()->count();
        $shareComments = $share->comments->count();
        $shareShares = 0; // Shares don't have shares

        // Get top 3 reaction types for display
        $shareTopReactions = $share->reactions()
            ->select('type', \DB::raw('count(*) as count'))
            ->groupBy('type')
            ->orderBy('count', 'desc')
            ->limit(3)
            ->get();
    ?>

    <div id="share-summary-bar-<?php echo e($share->id); ?>" class="px-4 py-3 border-t border-gray-200" style="display: <?php echo e(($shareReactions > 0 || $shareComments > 0) ? 'block' : 'none'); ?>">
        <div class="flex items-center justify-between text-sm text-gray-600">
            <!-- Left: Reaction emojis and count -->
            <div class="flex items-center space-x-2">
                <div id="share-reaction-summary-<?php echo e($share->id); ?>" class="flex items-center space-x-1" style="display: <?php echo e($shareReactions > 0 ? 'flex' : 'none'); ?>">
                    <!-- Show top reaction emojis -->
                    <div id="share-reaction-emojis-<?php echo e($share->id); ?>" class="flex -space-x-1">
                        <?php $__currentLoopData = $shareTopReactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $reactionDetails = \App\Models\Reaction::getReactionDetails($reaction->type);
                            ?>
                            <?php if($reactionDetails): ?>
                                <div class="w-5 h-5 rounded-full bg-white border border-gray-200 flex items-center justify-center">
                                    <img src="<?php echo e($reactionDetails['emoji']); ?>" alt="<?php echo e($reactionDetails['label']); ?>" class="w-4 h-4">
                                </div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <span id="share-reaction-count-<?php echo e($share->id); ?>" class="font-medium"><?php echo e($shareReactions); ?></span>
                </div>
            </div>

            <!-- Right: Comments -->
            <div class="flex items-center space-x-4">
                <span id="share-comment-summary-<?php echo e($share->id); ?>" class="hover:underline cursor-pointer" onclick="openShareCommentModal(<?php echo e($share->id); ?>)" style="display: <?php echo e($shareComments > 0 ? 'inline' : 'none'); ?>">
                    <?php echo e($shareComments); ?> comment<?php echo e($shareComments !== 1 ? 's' : ''); ?>

                </span>
            </div>
        </div>
    </div>

    <!-- Post Actions for SHARE -->
    <div class="px-4 py-3 bg-gray-50 border-t border-gray-200">
        <div class="flex items-center justify-between">
            <div class="flex items-center justify-around w-full">
                <!-- Facebook-style Reactions for SHARE -->
                <?php if (isset($component)) { $__componentOriginala21421101ecd1a67ffa8905ddbe200b3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala21421101ecd1a67ffa8905ddbe200b3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.facebook-reactions','data' => ['target' => $share,'targetType' => 'share']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('facebook-reactions'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['target' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($share),'target-type' => 'share']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala21421101ecd1a67ffa8905ddbe200b3)): ?>
<?php $attributes = $__attributesOriginala21421101ecd1a67ffa8905ddbe200b3; ?>
<?php unset($__attributesOriginala21421101ecd1a67ffa8905ddbe200b3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala21421101ecd1a67ffa8905ddbe200b3)): ?>
<?php $component = $__componentOriginala21421101ecd1a67ffa8905ddbe200b3; ?>
<?php unset($__componentOriginala21421101ecd1a67ffa8905ddbe200b3); ?>
<?php endif; ?>

                <button onclick="openShareCommentModal(<?php echo e($share->id); ?>)" class="flex items-center space-x-2 text-gray-500 hover:text-blue-600 transition-colors py-2 px-4 rounded-lg hover:bg-gray-100">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <span class="text-sm font-medium">Comment</span>
                </button>

                <button onclick="openShareModal(<?php echo e($share->post ? $share->post->id : 'null'); ?>)" class="flex items-center space-x-2 text-gray-500 hover:text-blue-600 transition-colors py-2 px-4 rounded-lg hover:bg-gray-100">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                    </svg>
                    <span class="text-sm font-medium">Share</span>
                </button>

                <!-- View Original Post Button -->
                <button onclick="viewOriginalPost(<?php echo e($share->post ? $share->post->id : 'null'); ?>)"
                        class="flex items-center space-x-2 text-gray-500 hover:text-purple-600 transition-colors py-2 px-4 rounded-lg hover:bg-gray-100">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                    <span class="text-sm font-medium">View Original</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Inline Comments Section for SHARE (hidden by default) -->
    <div id="share-comments-section-<?php echo e($share->id); ?>" class="hidden border-t border-gray-200">
        <?php if (isset($component)) { $__componentOriginale2adaab6a901bf856821fcceeedc4714 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale2adaab6a901bf856821fcceeedc4714 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.share-comment-section','data' => ['share' => $share,'showInline' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('share-comment-section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['share' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($share),'showInline' => true]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale2adaab6a901bf856821fcceeedc4714)): ?>
<?php $attributes = $__attributesOriginale2adaab6a901bf856821fcceeedc4714; ?>
<?php unset($__attributesOriginale2adaab6a901bf856821fcceeedc4714); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale2adaab6a901bf856821fcceeedc4714)): ?>
<?php $component = $__componentOriginale2adaab6a901bf856821fcceeedc4714; ?>
<?php unset($__componentOriginale2adaab6a901bf856821fcceeedc4714); ?>
<?php endif; ?>
    </div>

    <!-- Share Comment Modal -->
    <?php if (isset($component)) { $__componentOriginal0a7a687ed9716f77896d6b16e8c78525 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0a7a687ed9716f77896d6b16e8c78525 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.share-comment-modal','data' => ['share' => $share]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('share-comment-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['share' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($share)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0a7a687ed9716f77896d6b16e8c78525)): ?>
<?php $attributes = $__attributesOriginal0a7a687ed9716f77896d6b16e8c78525; ?>
<?php unset($__attributesOriginal0a7a687ed9716f77896d6b16e8c78525); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0a7a687ed9716f77896d6b16e8c78525)): ?>
<?php $component = $__componentOriginal0a7a687ed9716f77896d6b16e8c78525; ?>
<?php unset($__componentOriginal0a7a687ed9716f77896d6b16e8c78525); ?>
<?php endif; ?>

    <!-- Share Modal for Original Post -->
    <?php if($share->post): ?>
        <?php if (isset($component)) { $__componentOriginal0dc3624784f5ac950932d2feff9a6435 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0dc3624784f5ac950932d2feff9a6435 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.share-modal','data' => ['post' => $share->post]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('share-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['post' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($share->post)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0dc3624784f5ac950932d2feff9a6435)): ?>
<?php $attributes = $__attributesOriginal0dc3624784f5ac950932d2feff9a6435; ?>
<?php unset($__attributesOriginal0dc3624784f5ac950932d2feff9a6435); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0dc3624784f5ac950932d2feff9a6435)): ?>
<?php $component = $__componentOriginal0dc3624784f5ac950932d2feff9a6435; ?>
<?php unset($__componentOriginal0dc3624784f5ac950932d2feff9a6435); ?>
<?php endif; ?>
    <?php endif; ?>
</div>

<script>
// Toggle share comments section
function toggleShareComments(shareId) {
    const commentsSection = document.getElementById(`share-comments-section-${shareId}`);
    if (commentsSection.classList.contains('hidden')) {
        commentsSection.classList.remove('hidden');
    } else {
        commentsSection.classList.add('hidden');
    }
}

// Share Modal Functions (for original post)
function openShareModal(postId) {
    console.log('Opening share modal for post ID:', postId);
    if (!postId || postId === 'null') {
        console.error('Invalid post ID for share modal');
        alert('Error: Cannot share this post. Post ID is missing.');
        return;
    }
    const modal = document.getElementById(`shareModal-${postId}`);
    if (modal) {
        modal.classList.remove('hidden');
    } else {
        console.error('Share modal not found for post ID:', postId);
    }
}

function closeShareModal(postId) {
    if (!postId || postId === 'null') return;
    const modal = document.getElementById(`shareModal-${postId}`);
    if (modal) {
        modal.classList.add('hidden');
    }
}

// View original post
function viewOriginalPost(postId) {
    console.log('Viewing original post ID:', postId);
    if (!postId || postId === 'null') {
        console.error('Invalid post ID for viewing original post');
        alert('Error: Cannot view original post. Post ID is missing.');
        return;
    }
    window.location.href = `/posts/${postId}`;
}

// Share comment modal functions
function openShareCommentModal(shareId) {
    const modal = document.getElementById(`shareCommentModal-${shareId}`);
    modal.classList.remove('hidden');
    document.body.style.overflow = 'hidden';

    // Initialize reaction system for modal content
    setTimeout(() => {
        if (window.facebookReactionSystem) {
            window.facebookReactionSystem.createReactionPopups();
        }
    }, 100);
}

function closeShareCommentModal(shareId, event = null) {
    if (event && event.target !== event.currentTarget) {
        return;
    }
    document.getElementById(`shareCommentModal-${shareId}`).classList.add('hidden');
    document.body.style.overflow = 'auto';
}

// Initialize Facebook reactions for shared posts when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize reaction system for shared posts
    if (window.facebookReactionSystem) {
        window.facebookReactionSystem.createReactionPopups();
    }

    // Handle share comment form submissions (exclude modal forms)
    const shareCommentForms = document.querySelectorAll('.share-comment-form');
    shareCommentForms.forEach(form => {
        // Skip forms that are inside modals (they're handled by comment-modal.js)
        if (form.closest('[id^="shareCommentModal-"]')) {
            return;
        }

        form.addEventListener('submit', async function(e) {
            e.preventDefault();

            const shareId = this.dataset.shareId;
            const formData = new FormData(this);

            try {
                const response = await fetch(`/shares/${shareId}/comments`, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();

                if (data.success) {
                    // Reset form
                    this.reset();

                    // Add comment to DOM
                    addShareCommentToDOM(shareId, data.comment);

                    // Update comment count
                    updateShareCommentCount(shareId);

                    // Initialize reactions for new comment
                    if (window.facebookReactionSystem) {
                        window.facebookReactionSystem.createReactionPopups();
                    }
                } else {
                    alert('Error adding comment: ' + (data.message || 'Unknown error'));
                }
            } catch (error) {
                console.error('Error adding share comment:', error);
                alert('Error adding comment. Please try again.');
            }
        });
    });

    // Handle share comment reply forms
    const shareReplyForms = document.querySelectorAll('.share-comment-reply-form');
    shareReplyForms.forEach(form => {
        form.addEventListener('submit', async function(e) {
            e.preventDefault();

            const shareId = this.dataset.shareId;
            const parentId = this.dataset.parentId;
            const formData = new FormData(this);
            formData.append('parent_id', parentId);

            try {
                const response = await fetch(`/shares/${shareId}/comments`, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();

                if (data.success) {
                    // Reset form and hide it
                    this.reset();
                    toggleShareCommentReply(parentId);

                    // Add reply to DOM
                    addShareReplyToDOM(parentId, data.comment);

                    // Initialize reactions for new reply
                    if (window.facebookReactionSystem) {
                        window.facebookReactionSystem.createReactionPopups();
                    }
                } else {
                    alert('Error adding reply: ' + (data.message || 'Unknown error'));
                }
            } catch (error) {
                console.error('Error adding share reply:', error);
                alert('Error adding reply. Please try again.');
            }
        });
    });
});

// Helper functions for share comments
function addShareCommentToDOM(shareId, comment) {
    const commentsList = document.getElementById(`share-comments-list-${shareId}`);
    if (!commentsList) return;

    // Remove "no comments" message if it exists
    const noComments = commentsList.querySelector('.no-comments');
    if (noComments) {
        noComments.remove();
    }

    // Create comment HTML (simplified version)
    const commentHTML = createShareCommentHTML(comment);

    // Add to the top of comments list
    commentsList.insertAdjacentHTML('afterbegin', commentHTML);
}

function addShareReplyToDOM(parentId, reply) {
    const parentComment = document.querySelector(`[data-comment-id="${parentId}"]`);
    if (!parentComment) return;

    let nestedComments = parentComment.querySelector('.nested-comments');
    if (!nestedComments) {
        // Create nested comments container if it doesn't exist
        const replyHTML = `<div class="nested-comments mt-4 ml-4 space-y-3 border-l-2 border-gray-100 pl-4"></div>`;
        parentComment.insertAdjacentHTML('beforeend', replyHTML);
        nestedComments = parentComment.querySelector('.nested-comments');
    }

    // Create reply HTML
    const replyHTML = createShareCommentHTML(reply, true);

    // Add to the bottom of replies
    nestedComments.insertAdjacentHTML('beforeend', replyHTML);
}

function createShareCommentHTML(comment, isReply = false) {
    const timeAgo = formatTimeAgo(comment.created_at);
    const userAvatar = comment.user.avatar
        ? `/storage/${comment.user.avatar}`
        : `https://ui-avatars.com/api/?name=${encodeURIComponent(comment.user.name)}&color=7BC74D&background=EEEEEE`;

    const avatarSize = isReply ? 'h-8 w-8' : 'h-10 w-10';
    const containerClass = isReply ? 'comment-item' : 'comment-item py-4 px-4 hover:bg-gray-50/50 transition-colors duration-150';

    return `
        <div class="${containerClass}" data-comment-id="${comment.id}">
            <div class="flex space-x-3">
                <a href="/profile/${comment.user.id}" class="flex-shrink-0">
                    <img class="${avatarSize} rounded-full ring-1 ring-gray-200 shadow-sm"
                         src="${userAvatar}"
                         alt="${comment.user.name}">
                </a>
                <div class="flex-1 min-w-0">
                    <div class="bg-gray-50/70 rounded-xl p-${isReply ? '3' : '4'} shadow-sm border border-gray-100">
                        <div class="flex items-center space-x-2 mb-2">
                            <a href="/profile/${comment.user.id}" class="font-semibold text-gray-900 hover:text-custom-green text-sm hover:underline">
                                ${comment.user.name}
                            </a>
                            <span class="text-xs text-gray-500">${timeAgo}</span>
                        </div>
                        <div class="comment-content">
                            <p class="text-gray-700 text-sm leading-relaxed">${comment.content.replace(/\n/g, '<br>')}</p>
                        </div>
                    </div>

                    <div class="comment-actions flex items-center space-x-4 mt-2 ml-2 text-sm">
                        <button onclick="toggleShareCommentLike(${comment.id})"
                                class="flex items-center space-x-1 text-gray-500 hover:text-red-600 transition-colors group"
                                id="share-comment-like-btn-${comment.id}">
                            <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                            </svg>
                            <span id="share-comment-like-count-${comment.id}" class="font-medium">0</span>
                        </button>
                        ${!isReply ? `
                        <button onclick="toggleShareCommentReply(${comment.id})" class="flex items-center space-x-1 text-gray-500 hover:text-blue-600 transition-colors group">
                            <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                            </svg>
                            <span class="font-medium">Reply</span>
                        </button>
                        ` : ''}
                    </div>
                </div>
            </div>
        </div>
    `;
}

function updateShareCommentCount(shareId) {
    // Update comment count in summary bar
    const commentSummary = document.getElementById(`share-comment-summary-${shareId}`);
    const commentCount = document.getElementById(`share-comments-count-${shareId}`);

    if (commentCount) {
        const currentCount = parseInt(commentCount.textContent) + 1;
        commentCount.textContent = currentCount;

        if (commentSummary) {
            commentSummary.style.display = 'inline';
            commentSummary.textContent = `${currentCount} comment${currentCount !== 1 ? 's' : ''}`;
        }
    }
}

function toggleShareCommentReply(commentId) {
    const replyForm = document.getElementById(`share-reply-form-${commentId}`);
    if (replyForm) {
        if (replyForm.classList.contains('hidden')) {
            replyForm.classList.remove('hidden');
            const textarea = replyForm.querySelector('textarea');
            if (textarea) {
                textarea.focus();
            }
        } else {
            replyForm.classList.add('hidden');
        }
    }
}

function cancelShareCommentReply(commentId) {
    const replyForm = document.getElementById(`share-reply-form-${commentId}`);
    if (replyForm) {
        replyForm.classList.add('hidden');
        replyForm.querySelector('form').reset();
    }
}

// Format time ago helper function
function formatTimeAgo(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;

    return date.toLocaleDateString();
}
</script>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/components/shared-post-card.blade.php ENDPATH**/ ?>